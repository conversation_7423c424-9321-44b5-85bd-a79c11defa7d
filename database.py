import sqlite3
import os
from config import Config

def get_db_connection():
    """
    Obtém uma conexão com o banco de dados com base nas configurações.

    Returns:
        Uma conexão com o banco de dados configurado.
    """
    db_type = Config.DB_TYPE.lower()

    if db_type == 'sqlite':
        conn = sqlite3.connect(Config.SQLITE_DB_PATH)
        conn.row_factory = sqlite3.Row
        return conn

    elif db_type == 'postgresql':
        try:
            import psycopg2
            from psycopg2.extras import RealDictCursor

            conn = psycopg2.connect(
                user=Config.POSTGRES_USER,
                password=Config.POSTGRES_PASSWORD,
                host=Config.POSTGRES_HOST,
                port=Config.POSTGRES_PORT,
                database=Config.POSTGRES_DB
            )
            conn.cursor_factory = RealDictCursor
            return conn
        except ImportError:
            print("Módulo psycopg2 não encontrado. Instalando com: pip install psycopg2-binary")
            raise
        except Exception as e:
            print(f"Erro ao conectar ao PostgreSQL: {e}")
            # Fallback para SQLite em caso de erro
            print("Usando SQLite como fallback")
            conn = sqlite3.connect(Config.SQLITE_DB_PATH)
            conn.row_factory = sqlite3.Row
            return conn

    elif db_type == 'mysql':
        try:
            import mysql.connector

            conn = mysql.connector.connect(
                user=Config.MYSQL_USER,
                password=Config.MYSQL_PASSWORD,
                host=Config.MYSQL_HOST,
                port=Config.MYSQL_PORT,
                database=Config.MYSQL_DB,
                autocommit=True
            )
            return conn
        except ImportError:
            print("Módulo mysql-connector-python não encontrado. Instalando com: pip install mysql-connector-python")
            raise
        except Exception as e:
            print(f"Erro ao conectar ao MySQL: {e}")
            # Fallback para SQLite em caso de erro
            print("Usando SQLite como fallback")
            conn = sqlite3.connect(Config.SQLITE_DB_PATH)
            conn.row_factory = sqlite3.Row
            return conn

    else:
        # Tipo de banco de dados não suportado, usar SQLite como padrão
        print(f"Tipo de banco de dados '{db_type}' não suportado. Usando SQLite.")
        conn = sqlite3.connect(Config.SQLITE_DB_PATH)
        conn.row_factory = sqlite3.Row
        return conn

def init_db():
    """
    Inicializa o banco de dados com as tabelas necessárias.
    Esta função centraliza a inicialização de todas as tabelas do sistema.
    """
    # Inicializa as tabelas de atualizações
    from updates.database import init_db as init_updates_db
    init_updates_db()

    print("Banco de dados inicializado com sucesso!")

def close_connection(conn):
    """
    Fecha a conexão com o banco de dados de forma segura.

    Args:
        conn: A conexão com o banco de dados a ser fechada.
    """
    if conn:
        try:
            conn.close()
        except Exception as e:
            print(f"Erro ao fechar conexão com o banco de dados: {e}")
