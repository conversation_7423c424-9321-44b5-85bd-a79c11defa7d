from os import environ, path
from dotenv import load_dotenv

basedir = path.abspath(path.dirname(__file__))
load_dotenv(path.join(basedir, ".env"))


class Config:
    # Configurações básicas
    SECRET_KEY = environ.get("SECRET_KEY") or "dev-key"
    MAINTENANCE_MODE = False
    ALLOWED_HOSTS = environ.get("ALLOWED_HOSTS", "localhost").split(",")
    ALLOWED_ORIGINS = [".vercel.app", ".fly.dev", "localhost:5000", "127.0.0.1:5000"]
    DEBUG = bool(int(environ.get("FLASK_DEBUG", "0")))
    TEMPLATES_AUTO_RELOAD = True
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = "Lax"
    COMPRESS_MIMETYPES = [
        "text/html",
        "text/css",
        "text/xml",
        "application/json",
        "application/javascript",
    ]
    STATIC_FOLDER = "static"
    ADMIN_PASSWORD = environ.get("ADMIN_PASSWORD") or "change-me-in-production"

    # Configurações de banco de dados
    DB_TYPE = environ.get("DB_TYPE", "sqlite")  # sqlite, postgresql, mysql

    # SQLite
    SQLITE_DB_PATH = environ.get("SQLITE_DB_PATH", path.join(basedir, "database.db"))

    # PostgreSQL
    POSTGRES_USER = environ.get("POSTGRES_USER", "postgres")
    POSTGRES_PASSWORD = environ.get("POSTGRES_PASSWORD", "")
    POSTGRES_HOST = environ.get("POSTGRES_HOST", "localhost")
    POSTGRES_PORT = environ.get("POSTGRES_PORT", "5432")
    POSTGRES_DB = environ.get("POSTGRES_DB", "iamshiuba")

    # MySQL
    MYSQL_USER = environ.get("MYSQL_USER", "root")
    MYSQL_PASSWORD = environ.get("MYSQL_PASSWORD", "")
    MYSQL_HOST = environ.get("MYSQL_HOST", "localhost")
    MYSQL_PORT = environ.get("MYSQL_PORT", "3306")
    MYSQL_DB = environ.get("MYSQL_DB", "iamshiuba")

    # Configurações de conexão
    DB_POOL_SIZE = int(environ.get("DB_POOL_SIZE", "10"))
    DB_MAX_OVERFLOW = int(environ.get("DB_MAX_OVERFLOW", "20"))
    DB_POOL_TIMEOUT = int(environ.get("DB_POOL_TIMEOUT", "30"))
    DB_POOL_RECYCLE = int(environ.get("DB_POOL_RECYCLE", "1800"))  # 30 minutos
