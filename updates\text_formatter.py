import re

def format_text_to_html(text):
    """
    Converte texto simples em HTML formatado.
    Regras:
    - Quebras de linha viram <br>
    - Links são detectados e convertidos em <a>
    - ** texto ** vira <strong>texto</strong>
    - * texto * vira <em>texto</em>
    - ### texto vira <h3>texto</h3>
    - ## texto vira <h2>texto</h2>
    - # texto vira <h1>texto</h1>
    """
    if not text:
        return ""

    # Converte cabeçalhos (deve ser feito primeiro)
    text = re.sub(r'^### (.*?)$', r'<h3>\1</h3>', text, flags=re.MULTILINE)
    text = re.sub(r'^## (.*?)$', r'<h2>\1</h2>', text, flags=re.MULTILINE)
    text = re.sub(r'^# (.*?)$', r'<h1>\1</h1>', text, flags=re.MULTILINE)
    
    # Converte negrito e itálico
    text = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', text)
    text = re.sub(r'\*(.*?)\*', r'<em>\1</em>', text)
    
    # Detecta e converte URLs em links
    url_pattern = r'https?://[^\s<>"]+|www\.[^\s<>"]+'
    text = re.sub(url_pattern, lambda x: f'<a href="{x.group()}" target="_blank" rel="noopener noreferrer">{x.group()}</a>', text)
    
    # Converte quebras de linha em <br>
    text = text.replace('\n', '<br>')
    
    return text

def format_preview_link(url):
    """
    Formata um link de prévia em um elemento HTML apropriado.
    Se for um link do YouTube, tenta extrair o ID do vídeo.
    """
    if not url:
        return ""
        
    # Detecta links do YouTube
    youtube_patterns = [
        r'(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\s]+)',
        r'(?:https?:\/\/)?(?:www\.)?youtube\.com\/embed\/([^&\s]+)'
    ]
    
    for pattern in youtube_patterns:
        match = re.search(pattern, url)
        if match:
            video_id = match.group(1)
            return f'''
            <div class="preview-embed">
                <iframe 
                    width="100%" 
                    height="315" 
                    src="https://www.youtube.com/embed/{video_id}" 
                    frameborder="0" 
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                    allowfullscreen>
                </iframe>
            </div>
            '''
    
    # Para outros links, cria um botão de prévia
    return f'''
    <div class="preview-link">
        <a href="{url}" target="_blank" rel="noopener noreferrer" class="preview-button">
            <i class="fas fa-external-link-alt"></i> Ver prévia
        </a>
    </div>
    '''
