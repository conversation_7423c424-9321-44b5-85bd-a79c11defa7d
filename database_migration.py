from typing import List, Dict, Any, Optional
import sqlite3
from pathlib import Path

def backup_table(conn: sqlite3.Connection, table_name: str) -> List[Dict[str, Any]]:
    """Faz backup dos dados de uma tabela."""
    data = conn.execute(f'SELECT * FROM {table_name}').fetchall()
    columns = [description[0] for description in conn.execute(f'SELECT * FROM {table_name}').description]
    return [{columns[i]: row[i] for i in range(len(columns))} for row in data]

def get_table_info(conn: sqlite3.Connection, table_name: str) -> List[Dict[str, Any]]:
    """Obtém informações sobre a estrutura da tabela."""
    return conn.execute(f'PRAGMA table_info({table_name})').fetchall()

def create_table_from_schema(conn: sqlite3.Connection, table_name: str, schema_function) -> None:
    """Cria uma nova tabela usando a função de schema fornecida."""
    schema_function(conn)

def restore_data(conn: sqlite3.Connection, table_name: str, old_data: List[Dict[str, Any]], new_columns: List[str]) -> None:
    """Restaura os dados na nova tabela, mapeando apenas as colunas existentes."""
    if not old_data:
        return

    # Filtra apenas as colunas que existem na nova tabela
    valid_columns = [col for col in old_data[0].keys() if col in new_columns]
    placeholders = ','.join(['?' for _ in valid_columns])
    columns = ','.join(valid_columns)

    for row in old_data:
        values = [row[col] for col in valid_columns]
        conn.execute(f'INSERT INTO {table_name} ({columns}) VALUES ({placeholders})', values)

def migrate_table(db_path: str, table_name: str, schema_function) -> None:
    """
    Migra uma tabela para um novo schema, preservando os dados existentes.
    
    Args:
        db_path: Caminho para o arquivo do banco de dados SQLite
        table_name: Nome da tabela a ser migrada
        schema_function: Função que cria o novo schema da tabela
    """
    print(f"Iniciando migração da tabela {table_name}...")
    
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    
    try:
        # Backup dos dados existentes
        old_data = backup_table(conn, table_name)
        print(f"Backup de {len(old_data)} registros realizado.")

        # Drop da tabela antiga
        conn.execute(f'DROP TABLE IF EXISTS {table_name}')
        conn.commit()
        print("Tabela antiga removida.")

        # Cria nova tabela com o novo schema
        create_table_from_schema(conn, table_name, schema_function)
        print("Nova tabela criada com o schema atualizado.")

        # Obtém as colunas da nova tabela
        new_columns = [col[1] for col in get_table_info(conn, table_name)]

        # Restaura os dados
        restore_data(conn, table_name, old_data, new_columns)
        conn.commit()
        print(f"Dados restaurados com sucesso na tabela {table_name}.")

    except Exception as e:
        print(f"Erro durante a migração: {str(e)}")
        conn.rollback()
        raise
    finally:
        conn.close()
        print("Conexão com o banco de dados fechada.")
