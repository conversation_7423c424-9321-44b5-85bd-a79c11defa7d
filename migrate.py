from database_migration import migrate_table
from updates.database import init_db as init_updates_db
import os

def migrate_all():
    db_path = os.path.join(os.path.dirname(__file__), 'database.db')

    # Migra a tabela de updates
    migrate_table(db_path, 'updates', init_updates_db)

if __name__ == '__main__':
    print("Iniciando migração do banco de dados...")
    migrate_all()
    print("Migração concluída com sucesso!")
